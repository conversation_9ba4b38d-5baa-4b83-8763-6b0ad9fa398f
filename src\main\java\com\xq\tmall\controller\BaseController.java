package com.xq.tmall.controller;

import com.xq.tmall.entity.User;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.TokenUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * 基控制器
 */
public class BaseController {
    //log4j
    protected Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);

    @Autowired
    protected UserService userService;

    //检查管理员权限
    protected Object checkAdmin(HttpSession session){
        Object o = session.getAttribute("adminId");
        if(o==null){
            logger.info("无管理权限，返回管理员登陆页");
            return null;
        }
        logger.info("权限验证成功，管理员ID：{}",o);
        return o;
    }

    //检查用户是否登录（通过session，兼容旧代码）
    protected Object checkUser(HttpSession session){
        Object o = session.getAttribute("userId");
        if(o==null){
            logger.info("用户未登录");
            return null;
        }
        logger.info("用户已登录，用户ID：{}", o);
        return o;
    }
    
    //检查用户是否登录（通过请求头）
    protected User checkUserFromToken(HttpServletRequest request){
        Integer userId = TokenUtil.getUserIdFromRequest();
        if(userId == null){
            logger.info("用户未登录");
            return null;
        }
        User user = userService.get(userId);
        if(user == null){
            logger.info("用户不存在");
            return null;
        }
        logger.info("用户已登录，用户ID：{}", userId);
        return user;
    }
}
