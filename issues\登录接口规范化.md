# 登录接口规范化计划

## 背景
项目传递参数不规范，需要将前端JSP发送的参数改为DTO对象数据格式为JSON，后端返回的参数格式也为JSON并且为VO对象使用Result封装。同时不使用session，改为使用localStorage存储用户信息。

## 实施计划

### 1. 创建DTO和VO对象
- 创建`UserLoginDTO.java`：包含用户名、密码和验证码
- 创建`UserLoginVO.java`：包含用户基本信息（不含敏感信息）

### 2. 修改前端代码
- 修改`fore_login.js`：将表单数据转换为JSON格式发送
- 创建`fore_user.js`：用于处理用户信息
- 更新验证码获取和验证逻辑：不再使用cookie存储验证码
- 修改`navigator.jsp`：使用localStorage中的用户信息

### 3. 修改后端代码
- 修改`ForeLoginController.java`：接收DTO对象，返回Result<UserLoginVO>
- 创建`TokenUtil.java`：用于验证用户信息
- 修改`BaseController.java`：支持从请求头中获取用户信息
- 更新验证码生成和验证逻辑

### 4. 测试
- 确保登录功能正常工作
- 验证前后端数据交互格式符合规范

## 已完成的工作
- 创建了DTO和VO对象
- 修改了前端代码，使用JSON格式发送数据
- 创建了用户信息处理工具类
- 修改了后端控制器，接收DTO对象并返回Result<UserLoginVO>
- 创建了TokenUtil工具类，用于验证用户信息
- 修改了BaseController，支持从请求头中获取用户信息 