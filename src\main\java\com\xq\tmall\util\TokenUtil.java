package com.xq.tmall.util;

import com.xq.tmall.entity.User;
import com.xq.tmall.service.UserService;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Token工具类
 */
public class TokenUtil {
    
    /**
     * 从请求头中获取用户ID
     * @return 用户ID，如果未登录则返回null
     */
    public static Integer getUserIdFromRequest() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String userId = request.getHeader("X-User-Id");
        if (userId != null && !userId.isEmpty()) {
            try {
                return Integer.parseInt(userId);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 验证用户是否登录
     * @param userService 用户服务
     * @return 用户对象，如果未登录则返回null
     */
    public static User validateUser(UserService userService) {
        Integer userId = getUserIdFromRequest();
        if (userId != null) {
            return userService.get(userId);
        }
        return null;
    }
} 