<%@ page contentType="text/html;charset=UTF-8" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/res/css/fore/fore_nav.css"/>
<script src="${pageContext.request.contextPath}/res/js/fore/fore_user.js"></script>
<script>
    $(function () {
        $(".quick_li").find("li").hover(
            function () {
                $(this).find(".sn_menu").addClass("sn_menu_hover");
                $(this).find(".quick_menu,.quick_qrcode,.quick_DirectPromoDiv,.quick_sitmap_div").css("display", "block");
            }, function () {
                $(this).find(".sn_menu").removeClass("sn_menu_hover");
                $(this).find(".quick_menu,.quick_qrcode,.quick_DirectPromoDiv,.quick_sitmap_div").css("display", "none");
            }
        );
        
        // 使用localStorage中的用户信息更新导航栏
        updateUserNav();
    });
    
    // 更新用户导航信息
    function updateUserNav() {
        var container = $("#container_login");
        if (userUtil.isLogin()) {
            // 用户已登录
            var nickname = userUtil.getNickname() || userUtil.getUsername();
            container.html(
                '<em>Hi，</em>' +
                '<a href="${pageContext.request.contextPath}/userDetails" class="userName" target="_blank">' + 
                nickname + 
                '</a>' +
                '<a href="javascript:void(0);" onclick="userUtil.logout()">退出</a>'
            );
        } else {
            // 用户未登录
            container.html(
                '<em>喵，欢迎来天猫</em>' +
                '<a href="${pageContext.request.contextPath}/login">请登录</a>' +
                '<a href="${pageContext.request.contextPath}/register">免费注册</a>'
            );
        }
    }
</script>
<div id="nav">
    <div class="nav_main">
        <p id="container_login">
            <!-- 这里的内容会被JavaScript动态替换 -->
            <em>喵，欢迎来天猫</em>
            <a href="${pageContext.request.contextPath}/login">请登录</a>
            <a href="${pageContext.request.contextPath}/register">免费注册</a>
        </p>
        <ul class="quick_li">
            <li class="quick_li_MyTaobao">
                <div class="sn_menu">
                    <a href="${pageContext.request.contextPath}/userDetails">我的淘宝<b></b></a>
                    <div class="quick_menu">
                        <a href="${pageContext.request.contextPath}/order/0/10">已买到的宝贝</a>
                    </div>
                </div>
            </li>
            <li class="quick_li_cart">
                <img src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/buyCar.png">
                <a href="${pageContext.request.contextPath}/cart">购物车</a>
            </li>
        </ul>
    </div>
</div>