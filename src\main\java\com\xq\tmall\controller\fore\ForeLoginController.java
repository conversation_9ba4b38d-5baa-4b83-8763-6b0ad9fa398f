package com.xq.tmall.controller.fore;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import com.xq.tmall.controller.BaseController;
import com.xq.tmall.entity.ApiVerCodeResp;
import com.xq.tmall.entity.User;
import com.xq.tmall.entity.dto.UserLoginDTO;
import com.xq.tmall.entity.vo.UserLoginVO;
import com.xq.tmall.result.Result;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 前台天猫-登陆页
 */
@Api(tags = "前台天猫-登陆页")
@Controller
@RequiredArgsConstructor
public class ForeLoginController extends BaseController {
    private final UserService userService;
    
    // 用于存储验证码信息，生产环境应使用Redis等缓存服务
    private static final Map<String, String> CODE_MAP = new ConcurrentHashMap<>();

    // 转到前台天猫-登录页
    @ApiOperation(value = "转到前台天猫-登录页", notes = "转到前台天猫-登录页")
    @GetMapping(value = "login")
    public String goToPage(HttpSession session, Map<String, Object> map) {
        // 转到前台天猫-登录页
        return "fore/loginPage";
    }

    // 登陆验证-ajax
    @ApiOperation(value = "登陆验证", notes = "登陆验证")
    @ResponseBody
    @PostMapping(value = "login/doLogin", produces = "application/json;charset=utf-8")
    public Result<UserLoginVO> checkLogin(@RequestBody UserLoginDTO loginDTO) {
        // 验证码校验
        String codeInServer = CODE_MAP.get(loginDTO.getVerToken());
        if (codeInServer == null || !codeInServer.equalsIgnoreCase(loginDTO.getCode())) {
            return Result.error("验证码错误或已过期");
        }
        
        // 清除验证码
        CODE_MAP.remove(loginDTO.getVerToken());
        
        // 用户验证登录
        User user = userService.login(loginDTO.getUsername(), loginDTO.getPassword());
        
        if (user == null) {
            // 登录验证失败
            return Result.error("用户名或密码错误");
        } else {
            // 登录验证成功，返回用户信息
            UserLoginVO userLoginVO = new UserLoginVO();
            userLoginVO.setUserId(user.getUser_id());
            userLoginVO.setUsername(user.getUser_name());
            userLoginVO.setNickname(user.getUser_nickname());
            userLoginVO.setProfilePicture(user.getUser_profile_picture_src());
            
            return Result.success(userLoginVO);
        }
    }

    // 退出当前账号
    @ApiOperation(value = "退出当前账号", notes = "退出当前账号")
    @GetMapping(value = "login/logout")
    public String logout() {
        // 前端清除localStorage中的用户信息即可
        return "redirect:/login";
    }

    @ApiOperation(value = "登录验证码", notes = "登录验证码")
    @ResponseBody
    @GetMapping(value = "login/code")
    public Result<ApiVerCodeResp> getVerCode() {
        CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(140, 38, 4, 20);
        String verToken = UUID.randomUUID().toString();
        String code = captcha.getCode().toLowerCase();
        
        // 将验证码存储在服务端
        CODE_MAP.put(verToken, code);
        
        ApiVerCodeResp verCodeResp = new ApiVerCodeResp(verToken, captcha.getImageBase64Data(), null);
        return Result.success(verCodeResp);
    }
}
