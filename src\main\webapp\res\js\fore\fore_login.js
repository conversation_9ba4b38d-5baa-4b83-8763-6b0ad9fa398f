$(function () {
    getHomeCode();
    //登录验证
    $(".loginForm").submit(function () {
        let yn = true;
        $(this).find(":text,:password").each(function () {
            let code = $("#code").val();
            if ($("#name").val() === "") {
                styleUtil.errorShow($("#error_message_p"), "请输入用户名！");
                yn = false;
                return yn;
            }
            if ($("#password").val() === "") {
                styleUtil.errorShow($("#error_message_p"), "请输入密码！");
                yn = false;
                return yn;
            }
            if ($("#code").val() === "") {
                styleUtil.errorShow($("#error_message_p"), "请输入验证码！");
                yn = false;
                return yn;
            }
        });

        if (yn) {
            // 创建登录DTO对象
            let loginData = {
                username: $.trim($("#name").val()),
                password: $.trim($("#password").val()),
                code: $.trim($("#code").val()),
                verToken: localStorage.getItem("verToken")
            };

            $.ajax({
                type: "POST",
                url: "/tmall/login/doLogin",
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(loginData),
                dataType: "json",
                success: function (result) {
                    $(".loginButton").val("登 录");
                    if (result.code === 1) {
                        // 登录成功，存储用户信息到localStorage
                        localStorage.setItem("userInfo", JSON.stringify(result.data));
                        location.href = "/tmall";
                        // 清除验证码token
                        localStorage.removeItem("verToken");
                    } else {
                        styleUtil.errorShow($("#error_message_p"), result.msg || "登录失败！");
                        // 刷新验证码
                        getHomeCode();
                    }
                },
                error: function () {
                    $(".loginButton").val("登 录");
                    styleUtil.errorShow($("#error_message_p"), "服务器异常，请刷新页面再试！");
                    // 刷新验证码
                    getHomeCode();
                },
                beforeSend: function () {
                    $(".loginButton").val("正在登录...");
                }
            });
        }
        return false;
    });
  /*  $(".loginForm :text,.loginForm :password").focus(function () {
        styleUtil.errorHide($("#error_message_p"));
    });*/
});

//获取登录验证码
function getHomeCode() {
    $.getJSON("/tmall/login/code",function (data) {
        if(data !== null && data.code === 1){
            let codeData = data.data;
            $("#img_code").attr("src", codeData.img);
            // 将验证码token存储在localStorage中
            localStorage.setItem("verToken", codeData.verToken);
        }
    });
}