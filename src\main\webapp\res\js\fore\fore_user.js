// 用户信息工具类
var userUtil = {
    // 获取用户信息
    getUserInfo: function() {
        var userInfoStr = localStorage.getItem("userInfo");
        if (userInfoStr) {
            try {
                return JSON.parse(userInfoStr);
            } catch (e) {
                console.error("解析用户信息失败", e);
                return null;
            }
        }
        return null;
    },
    
    // 判断用户是否登录
    isLogin: function() {
        return this.getUserInfo() !== null;
    },
    
    // 获取用户ID
    getUserId: function() {
        var userInfo = this.getUserInfo();
        return userInfo ? userInfo.userId : null;
    },
    
    // 获取用户名
    getUsername: function() {
        var userInfo = this.getUserInfo();
        return userInfo ? userInfo.username : null;
    },
    
    // 获取用户昵称
    getNickname: function() {
        var userInfo = this.getUserInfo();
        return userInfo ? userInfo.nickname : null;
    },
    
    // 获取用户头像
    getProfilePicture: function() {
        var userInfo = this.getUserInfo();
        return userInfo ? userInfo.profilePicture : null;
    },
    
    // 清除用户信息（登出）
    logout: function() {
        localStorage.removeItem("userInfo");
        // 跳转到登录页
        location.href = "/tmall/login";
    },
    
    // 设置全局AJAX请求头
    setupAjaxHeader: function() {
        $.ajaxSetup({
            beforeSend: function(xhr) {
                var userId = userUtil.getUserId();
                if (userId) {
                    xhr.setRequestHeader('X-User-Id', userId);
                }
            }
        });
    }
};

// 初始化设置AJAX请求头
$(function() {
    userUtil.setupAjaxHeader();
}); 